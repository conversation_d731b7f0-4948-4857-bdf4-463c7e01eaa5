import React, { useRef, useState, useCallback, useEffect, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
} from '@tanstack/react-table';

/**
 * 基于 AG Grid 优化策略的虚拟表格组件
 * 主要优化点：
 * 1. 动画帧调度 - 模拟 AG Grid 的 AnimationFrameService
 * 2. 智能缓冲区管理 - 根据滚动方向和速度动态调整
 * 3. 分层任务处理 - 优先级任务队列
 * 4. 预测性渲染 - 基于滚动速度预测需要渲染的行
 * 5. 防白屏机制 - 保持足够的缓冲区避免白屏
 */
function AgGridLikeTable({
  data,
  columns,
  height = 500,
  rowHeight = 48,
  rowBuffer = 10, // AG Grid 默认值
  debounceVerticalScrollbar = false,
  alwaysShowVerticalScroll = false,
  alwaysShowHorizontalScroll = false,
  suppressMaxRenderedRowRestriction = false,
  suppressAnimationFrame = false, // AG Grid 选项
}) {
  const tableContainerRef = useRef(null);
  const viewportRef = useRef(null);
  const contentRef = useRef(null);

  // 滚动状态
  const [scrollTop, setScrollTop] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollDirection, setScrollDirection] = useState({ vertical: 0, horizontal: 0 });
  const [scrollVelocity, setScrollVelocity] = useState({ vertical: 0, horizontal: 0 });

  // 渲染状态
  const [renderRange, setRenderRange] = useState({ start: 0, end: 0 });
  const [renderedRows, setRenderedRows] = useState(new Map());

  // 引用状态
  const scrollingTimerRef = useRef(null);
  const rafIdRef = useRef(null);
  const lastScrollPositionRef = useRef({ top: 0, left: 0 });
  const lastScrollTimeRef = useRef(0);
  const scrollSamplesRef = useRef({ vertical: [], horizontal: [] });
  const isScrollingRef = useRef(false);
  const scrollGoingDownRef = useRef(true);

  // 任务队列 - 模拟 AG Grid 的 AnimationFrameService
  const taskQueuesRef = useRef({
    p1: [], // 高优先级任务 - 行背景渲染
    p2: [], // 中优先级任务 - 单元格渲染
    destroy: [] // 销毁任务
  });
  const tickingRef = useRef(false);
  const taskCountRef = useRef(0);

  // 使用 TanStack Table
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  // 动画帧任务调度器 - 模拟 AG Grid AnimationFrameService
  const scheduleTask = useCallback((task, priority = 'p2', index = 0) => {
    if (suppressAnimationFrame) {
      task();
      return;
    }

    const taskItem = {
      task,
      index,
      createOrder: ++taskCountRef.current
    };

    taskQueuesRef.current[priority].push(taskItem);

    if (!tickingRef.current) {
      tickingRef.current = true;
      requestAnimationFrame(executeFrame);
    }
  }, [suppressAnimationFrame]);

  // 执行动画帧任务
  const executeFrame = useCallback(() => {
    const frameStart = Date.now();
    const maxFrameTime = 16; // 60fps
    let duration = 0;

    const { p1, p2, destroy } = taskQueuesRef.current;

    while (duration < maxFrameTime && (p1.length || p2.length || destroy.length)) {
      let task;

      if (p1.length) {
        // 按索引排序，优先渲染滚动方向的行
        p1.sort((a, b) => {
          if (scrollGoingDownRef.current) {
            return a.index - b.index;
          } else {
            return b.index - a.index;
          }
        });
        task = p1.pop().task;
      } else if (p2.length) {
        p2.sort((a, b) => {
          if (scrollGoingDownRef.current) {
            return a.index - b.index;
          } else {
            return b.index - a.index;
          }
        });
        task = p2.pop().task;
      } else if (destroy.length) {
        task = destroy.pop();
      }

      if (task) {
        task();
      }

      duration = Date.now() - frameStart;
    }

    if (p1.length || p2.length || destroy.length) {
      requestAnimationFrame(executeFrame);
    } else {
      tickingRef.current = false;
    }
  }, []);

  // 更新滚动指标
  const updateScrollMetrics = useCallback((newScrollTop, newScrollLeft) => {
    const now = performance.now();
    const timeDelta = now - lastScrollTimeRef.current;

    // 更新滚动方向
    scrollGoingDownRef.current = newScrollTop >= lastScrollPositionRef.current.top;

    if (timeDelta > 0 && lastScrollTimeRef.current > 0) {
      const verticalVelocity = (newScrollTop - lastScrollPositionRef.current.top) / timeDelta;
      const horizontalVelocity = (newScrollLeft - lastScrollPositionRef.current.left) / timeDelta;

      // 滚动样本平滑处理
      scrollSamplesRef.current.vertical.push(verticalVelocity);
      scrollSamplesRef.current.horizontal.push(horizontalVelocity);

      if (scrollSamplesRef.current.vertical.length > 3) {
        scrollSamplesRef.current.vertical.shift();
      }
      if (scrollSamplesRef.current.horizontal.length > 3) {
        scrollSamplesRef.current.horizontal.shift();
      }

      const avgVerticalVelocity = scrollSamplesRef.current.vertical.reduce((sum, v) => sum + v, 0) /
                                 scrollSamplesRef.current.vertical.length;
      const avgHorizontalVelocity = scrollSamplesRef.current.horizontal.reduce((sum, v) => sum + v, 0) /
                                   scrollSamplesRef.current.horizontal.length;

      setScrollVelocity({
        vertical: avgVerticalVelocity,
        horizontal: avgHorizontalVelocity
      });

      setScrollDirection({
        vertical: Math.sign(newScrollTop - lastScrollPositionRef.current.top),
        horizontal: Math.sign(newScrollLeft - lastScrollPositionRef.current.left)
      });
    }

    lastScrollPositionRef.current = { top: newScrollTop, left: newScrollLeft };
    lastScrollTimeRef.current = now;
  }, []);

  // 计算智能渲染范围 - 基于 AG Grid 的缓冲区策略
  const calculateRenderRange = useCallback(() => {
    if (!viewportRef.current) {
      return { start: 0, end: Math.min(data.length - 1, 50) };
    }

    const containerHeight = viewportRef.current.clientHeight;
    const visibleRowCount = Math.ceil(containerHeight / rowHeight);

    // 基础可见范围
    const visibleStart = Math.floor(scrollTop / rowHeight);
    const visibleEnd = Math.min(data.length - 1, visibleStart + visibleRowCount);

    // 动态缓冲区计算
    let dynamicBuffer = rowBuffer;

    // 根据滚动速度调整缓冲区
    const absVelocity = Math.abs(scrollVelocity.vertical);
    if (absVelocity > 0.5) {
      // 高速滚动时增加缓冲区
      dynamicBuffer = Math.min(rowBuffer * 3, Math.ceil(absVelocity * 20));
    }

    // 根据滚动方向优化缓冲区分配
    let bufferBefore = dynamicBuffer;
    let bufferAfter = dynamicBuffer;

    if (scrollDirection.vertical > 0) {
      // 向下滚动，增加下方缓冲区
      bufferAfter = Math.floor(dynamicBuffer * 1.5);
      bufferBefore = Math.floor(dynamicBuffer * 0.5);
    } else if (scrollDirection.vertical < 0) {
      // 向上滚动，增加上方缓冲区
      bufferBefore = Math.floor(dynamicBuffer * 1.5);
      bufferAfter = Math.floor(dynamicBuffer * 0.5);
    }

    const start = Math.max(0, visibleStart - bufferBefore);
    const end = Math.min(data.length - 1, visibleEnd + bufferAfter);

    // 限制最大渲染行数（AG Grid 默认 500）
    const maxRows = suppressMaxRenderedRowRestriction ? Infinity : 500;
    if (end - start + 1 > maxRows) {
      if (scrollDirection.vertical >= 0) {
        // 向下滚动时保持下方
        return { start: Math.max(0, end - maxRows + 1), end };
      } else {
        // 向上滚动时保持上方
        return { start, end: Math.min(data.length - 1, start + maxRows - 1) };
      }
    }

    return { start, end };
  }, [scrollTop, scrollVelocity, scrollDirection, rowBuffer, data.length, rowHeight, suppressMaxRenderedRowRestriction]);

  // 滚动处理函数
  const handleScroll = useCallback((event) => {
    const target = event.target;
    const newScrollTop = target.scrollTop;
    const newScrollLeft = target.scrollLeft;

    // 防抖处理
    if (debounceVerticalScrollbar && scrollingTimerRef.current) {
      clearTimeout(scrollingTimerRef.current);
    }

    const updateScroll = () => {
      updateScrollMetrics(newScrollTop, newScrollLeft);
      setScrollTop(newScrollTop);
      setScrollLeft(newScrollLeft);

      if (!isScrollingRef.current) {
        setIsScrolling(true);
        isScrollingRef.current = true;
      }

      // 清除滚动结束定时器
      if (scrollingTimerRef.current) {
        clearTimeout(scrollingTimerRef.current);
      }

      // 设置滚动结束检测
      scrollingTimerRef.current = setTimeout(() => {
        setIsScrolling(false);
        isScrollingRef.current = false;
        scrollSamplesRef.current = { vertical: [], horizontal: [] };
      }, 150);

      // 重新计算渲染范围
      const newRange = calculateRenderRange();
      if (newRange.start !== renderRange.start || newRange.end !== renderRange.end) {
        scheduleTask(() => {
          setRenderRange(newRange);
        }, 'p1');
      }
    };

    if (debounceVerticalScrollbar) {
      scrollingTimerRef.current = setTimeout(updateScroll, 10);
    } else {
      updateScroll();
    }
  }, [debounceVerticalScrollbar, updateScrollMetrics, calculateRenderRange, renderRange, scheduleTask]);

  // 行渲染管理 - 模拟 AG Grid 的行回收机制
  const manageRowRendering = useCallback((newRange) => {
    const newRenderedRows = new Map();

    // 回收不需要的行
    renderedRows.forEach((rowData, index) => {
      if (index < newRange.start || index > newRange.end) {
        scheduleTask(() => {
          // 销毁行组件的逻辑
        }, 'destroy');
      } else {
        newRenderedRows.set(index, rowData);
      }
    });

    // 创建新行
    for (let i = newRange.start; i <= newRange.end; i++) {
      if (!newRenderedRows.has(i) && i < data.length) {
        const rowData = data[i];
        scheduleTask(() => {
          newRenderedRows.set(i, rowData);
        }, 'p2', i);
      }
    }

    setRenderedRows(newRenderedRows);
  }, [renderedRows, data, scheduleTask]);

  // 监听渲染范围变化
  useEffect(() => {
    manageRowRendering(renderRange);
  }, [renderRange, manageRowRendering]);

  // 初始化渲染范围
  useEffect(() => {
    const initialRange = calculateRenderRange();
    setRenderRange(initialRange);
  }, [calculateRenderRange]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollingTimerRef.current) {
        clearTimeout(scrollingTimerRef.current);
      }
      if (rafIdRef.current) {
        cancelAnimationFrame(rafIdRef.current);
      }
    };
  }, []);

  // 渲染行组件
  const renderRow = useCallback((rowIndex) => {
    const row = table.getRowModel().rows[rowIndex];
    if (!row) return null;

    return (
      <div
        key={`row-${rowIndex}`}
        className="ag-row"
        style={{
          position: 'absolute',
          top: rowIndex * rowHeight,
          left: 0,
          right: 0,
          height: rowHeight,
          display: 'flex',
          borderBottom: '1px solid #e0e0e0'
        }}
      >
        {row.getVisibleCells().map((cell) => (
          <div
            key={cell.id}
            className="ag-cell"
            style={{
              flex: cell.column.getSize(),
              padding: '8px 12px',
              display: 'flex',
              alignItems: 'center',
              borderRight: '1px solid #e0e0e0'
            }}
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </div>
        ))}
      </div>
    );
  }, [table, rowHeight]);

  // 计算总高度
  const totalHeight = data.length * rowHeight;

  return (
    <div
      ref={tableContainerRef}
      className="ag-grid-container"
      style={{
        height,
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
        border: '1px solid #d0d0d0'
      }}
    >
      {/* 表头 */}
      <div
        className="ag-header"
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: '#f5f5f5',
          borderBottom: '2px solid #d0d0d0',
          display: 'flex'
        }}
      >
        {table.getHeaderGroups()[0]?.headers.map((header) => (
          <div
            key={header.id}
            className="ag-header-cell"
            style={{
              flex: header.getSize(),
              padding: '12px',
              fontWeight: 'bold',
              borderRight: '1px solid #d0d0d0',
              backgroundColor: '#f8f8f8'
            }}
          >
            {flexRender(header.column.columnDef.header, header.getContext())}
          </div>
        ))}
      </div>

      {/* 虚拟滚动视口 */}
      <div
        ref={viewportRef}
        className="ag-body-viewport"
        style={{
          height: height - 50, // 减去表头高度
          overflow: 'auto',
          position: 'relative'
        }}
        onScroll={handleScroll}
      >
        {/* 内容容器 */}
        <div
          ref={contentRef}
          className="ag-body-container"
          style={{
            height: totalHeight,
            position: 'relative',
            width: '100%'
          }}
        >
          {/* 渲染可见行 */}
          {Array.from({ length: renderRange.end - renderRange.start + 1 }, (_, index) => {
            const rowIndex = renderRange.start + index;
            return renderRow(rowIndex);
          })}
        </div>
      </div>

      {/* 滚动条配置 */}
      <style jsx>{`
        .ag-grid-container {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px;
        }

        .ag-body-viewport::-webkit-scrollbar {
          width: ${alwaysShowVerticalScroll ? '12px' : 'auto'};
          height: ${alwaysShowHorizontalScroll ? '12px' : 'auto'};
        }

        .ag-body-viewport::-webkit-scrollbar-track {
          background: #f1f1f1;
        }

        .ag-body-viewport::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 6px;
        }

        .ag-body-viewport::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        .ag-row:hover {
          background-color: #f0f8ff;
        }

        .ag-cell {
          user-select: text;
        }
      `}</style>
    </div>
  );
}

export default AgGridLikeTable;