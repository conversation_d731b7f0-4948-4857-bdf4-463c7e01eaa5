# 基于 AG Grid 的虚拟表格优化方案

## 问题分析

在使用 @tanstack/react-table 和 @tanstack/react-virtual 实现虚拟表格时，直接拖动滚动条容易出现白屏问题。这主要是因为：

1. **渲染延迟**: 快速滚动时，新行的渲染跟不上滚动速度
2. **缓冲区不足**: 可见区域外的缓冲行数太少
3. **任务调度问题**: 渲染任务没有合理的优先级管理
4. **滚动预测缺失**: 无法预测滚动方向和速度来提前准备

## AG Grid 的优化策略

通过分析 AG Grid 源码，发现其主要优化策略包括：

### 1. 动画帧任务调度 (AnimationFrameService)

```javascript
// AG Grid 使用分层任务队列
const taskQueues = {
  p1: [], // 高优先级 - 行背景渲染
  p2: [], // 中优先级 - 单元格渲染  
  f1: [], // 框架组件渲染
  destroy: [] // 销毁任务
};
```

**优化点**:
- 按优先级处理渲染任务
- 根据滚动方向排序任务
- 控制每帧执行时间（16ms）
- 延迟销毁不可见行

### 2. 智能缓冲区管理

```javascript
// 默认缓冲区配置
rowBuffer: 10 // AG Grid 默认值

// 动态缓冲区调整
if (scrollVelocity > threshold) {
  bufferSize = Math.min(rowBuffer * 3, velocity * 20);
}
```

**优化点**:
- 根据滚动速度动态调整缓冲区
- 滚动方向优化：向下滚动增加下方缓冲区
- 最大渲染行数限制（默认500行）

### 3. 滚动方向感知

```javascript
// 跟踪滚动方向
scrollGoingDown = scrollTop >= lastScrollTop;

// 优先渲染滚动方向的行
if (scrollGoingDown) {
  tasks.sort((a, b) => a.index - b.index); // 从上到下
} else {
  tasks.sort((a, b) => b.index - a.index); // 从下到上
}
```

### 4. 防抖和节流机制

```javascript
// 可选的滚动防抖
const onVScroll = debounceVerticalScrollbar
  ? debounce(handleScroll, SCROLL_DEBOUNCE_TIMEOUT)
  : handleScroll;
```

## 我们的优化实现

### 1. 动画帧调度器

```javascript
const scheduleTask = useCallback((task, priority = 'p2', index = 0) => {
  if (suppressAnimationFrame) {
    task();
    return;
  }
  
  const taskItem = { task, index, createOrder: ++taskCountRef.current };
  taskQueuesRef.current[priority].push(taskItem);
  
  if (!tickingRef.current) {
    tickingRef.current = true;
    requestAnimationFrame(executeFrame);
  }
}, [suppressAnimationFrame]);
```

### 2. 智能缓冲区计算

```javascript
const calculateRenderRange = useCallback(() => {
  // 动态缓冲区计算
  let dynamicBuffer = rowBuffer;
  
  // 根据滚动速度调整
  const absVelocity = Math.abs(scrollVelocity.vertical);
  if (absVelocity > 0.5) {
    dynamicBuffer = Math.min(rowBuffer * 3, Math.ceil(absVelocity * 20));
  }
  
  // 根据滚动方向优化分配
  let bufferBefore = dynamicBuffer;
  let bufferAfter = dynamicBuffer;
  
  if (scrollDirection.vertical > 0) {
    // 向下滚动，增加下方缓冲区
    bufferAfter = Math.floor(dynamicBuffer * 1.5);
    bufferBefore = Math.floor(dynamicBuffer * 0.5);
  } else if (scrollDirection.vertical < 0) {
    // 向上滚动，增加上方缓冲区
    bufferBefore = Math.floor(dynamicBuffer * 1.5);
    bufferAfter = Math.floor(dynamicBuffer * 0.5);
  }
  
  return { start, end };
}, [/* dependencies */]);
```

### 3. 滚动速度跟踪

```javascript
const updateScrollMetrics = useCallback((newScrollTop, newScrollLeft) => {
  const now = performance.now();
  const timeDelta = now - lastScrollTimeRef.current;
  
  if (timeDelta > 0) {
    const verticalVelocity = (newScrollTop - lastScrollPositionRef.current.top) / timeDelta;
    
    // 滚动样本平滑处理
    scrollSamplesRef.current.vertical.push(verticalVelocity);
    if (scrollSamplesRef.current.vertical.length > 3) {
      scrollSamplesRef.current.vertical.shift();
    }
    
    // 计算平均速度
    const avgVelocity = scrollSamplesRef.current.vertical.reduce((sum, v) => sum + v, 0) / 
                       scrollSamplesRef.current.vertical.length;
    
    setScrollVelocity({ vertical: avgVelocity });
  }
}, []);
```

### 4. 行渲染管理

```javascript
const manageRowRendering = useCallback((newRange) => {
  const newRenderedRows = new Map();
  
  // 回收不需要的行
  renderedRows.forEach((rowData, index) => {
    if (index < newRange.start || index > newRange.end) {
      scheduleTask(() => {
        // 延迟销毁
      }, 'destroy');
    } else {
      newRenderedRows.set(index, rowData);
    }
  });
  
  // 创建新行
  for (let i = newRange.start; i <= newRange.end; i++) {
    if (!newRenderedRows.has(i) && i < data.length) {
      scheduleTask(() => {
        newRenderedRows.set(i, data[i]);
      }, 'p2', i);
    }
  }
}, []);
```

## 配置选项

### 基础配置
- `rowHeight`: 行高（默认48px）
- `rowBuffer`: 缓冲区行数（默认10，AG Grid默认值）
- `height`: 表格高度

### 性能配置
- `suppressAnimationFrame`: 禁用动画帧调度
- `debounceVerticalScrollbar`: 启用滚动防抖
- `suppressMaxRenderedRowRestriction`: 移除最大渲染行数限制

### 滚动条配置
- `alwaysShowVerticalScroll`: 始终显示垂直滚动条
- `alwaysShowHorizontalScroll`: 始终显示水平滚动条

## 使用示例

```jsx
import AgGridLikeTable from './AgGridLikeTable';

function MyTable() {
  const data = generateLargeDataset(50000);
  const columns = defineColumns();
  
  return (
    <AgGridLikeTable
      data={data}
      columns={columns}
      height={600}
      rowHeight={48}
      rowBuffer={15}
      debounceVerticalScrollbar={false}
      suppressAnimationFrame={false}
      alwaysShowVerticalScroll={true}
    />
  );
}
```

## 性能对比

| 特性 | 原始实现 | 优化后 |
|------|----------|--------|
| 快速滚动白屏 | 经常出现 | 基本消除 |
| 滚动流畅度 | 一般 | 显著提升 |
| 大数据集性能 | 较差 | 良好 |
| 内存使用 | 较高 | 优化 |
| 渲染延迟 | 明显 | 最小化 |

## 关键优化效果

1. **消除白屏**: 通过智能缓冲区和预测性渲染
2. **提升流畅度**: 动画帧调度和任务优先级管理
3. **减少延迟**: 滚动方向感知和提前准备
4. **内存优化**: 延迟销毁和行回收机制
5. **可配置性**: 丰富的配置选项适应不同场景

这个优化方案基本复现了 AG Grid 的核心虚拟化策略，能够有效解决快速滚动时的白屏问题。
