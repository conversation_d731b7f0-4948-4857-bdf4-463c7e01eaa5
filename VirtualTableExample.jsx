import React, { useState, useMemo } from 'react';
import AgGridLikeTable from './AgGridLikeTable';

// 生成测试数据
const generateData = (count = 10000) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `用户 ${index + 1}`,
    email: `user${index + 1}@example.com`,
    age: Math.floor(Math.random() * 50) + 20,
    city: ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都'][Math.floor(Math.random() * 8)],
    department: ['技术部', '产品部', '运营部', '市场部', '人事部'][Math.floor(Math.random() * 5)],
    salary: Math.floor(Math.random() * 50000) + 50000,
    joinDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toLocaleDateString(),
    status: ['在职', '离职', '休假'][Math.floor(Math.random() * 3)],
    score: (Math.random() * 100).toFixed(1)
  }));
};

function VirtualTableExample() {
  const [dataSize, setDataSize] = useState(10000);
  const [tableConfig, setTableConfig] = useState({
    rowHeight: 48,
    rowBuffer: 10,
    debounceVerticalScrollbar: false,
    suppressAnimationFrame: false,
    suppressMaxRenderedRowRestriction: false
  });

  // 生成数据
  const data = useMemo(() => generateData(dataSize), [dataSize]);

  // 定义列
  const columns = useMemo(() => [
    {
      accessorKey: 'id',
      header: 'ID',
      size: 80,
      cell: ({ getValue }) => (
        <span style={{ fontWeight: 'bold', color: '#1976d2' }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'name',
      header: '姓名',
      size: 120,
      cell: ({ getValue }) => (
        <span style={{ fontWeight: '500' }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'email',
      header: '邮箱',
      size: 200,
      cell: ({ getValue }) => (
        <span style={{ color: '#666', fontSize: '13px' }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'age',
      header: '年龄',
      size: 80,
      cell: ({ getValue }) => (
        <span style={{ 
          padding: '2px 8px',
          backgroundColor: getValue() > 35 ? '#e3f2fd' : '#f3e5f5',
          borderRadius: '12px',
          fontSize: '12px'
        }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'city',
      header: '城市',
      size: 100,
      cell: ({ getValue }) => (
        <span style={{ 
          color: '#1976d2',
          fontWeight: '500'
        }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'department',
      header: '部门',
      size: 120,
      cell: ({ getValue }) => {
        const colors = {
          '技术部': '#4caf50',
          '产品部': '#ff9800',
          '运营部': '#2196f3',
          '市场部': '#9c27b0',
          '人事部': '#f44336'
        };
        return (
          <span style={{ 
            color: colors[getValue()] || '#666',
            fontWeight: '500'
          }}>
            {getValue()}
          </span>
        );
      }
    },
    {
      accessorKey: 'salary',
      header: '薪资',
      size: 120,
      cell: ({ getValue }) => (
        <span style={{ 
          color: '#4caf50',
          fontWeight: 'bold'
        }}>
          ¥{getValue().toLocaleString()}
        </span>
      )
    },
    {
      accessorKey: 'joinDate',
      header: '入职日期',
      size: 120,
      cell: ({ getValue }) => (
        <span style={{ color: '#666', fontSize: '13px' }}>
          {getValue()}
        </span>
      )
    },
    {
      accessorKey: 'status',
      header: '状态',
      size: 80,
      cell: ({ getValue }) => {
        const statusColors = {
          '在职': '#4caf50',
          '离职': '#f44336',
          '休假': '#ff9800'
        };
        return (
          <span style={{ 
            color: statusColors[getValue()],
            fontWeight: 'bold',
            fontSize: '12px'
          }}>
            {getValue()}
          </span>
        );
      }
    },
    {
      accessorKey: 'score',
      header: '评分',
      size: 80,
      cell: ({ getValue }) => {
        const score = parseFloat(getValue());
        return (
          <span style={{ 
            color: score >= 80 ? '#4caf50' : score >= 60 ? '#ff9800' : '#f44336',
            fontWeight: 'bold'
          }}>
            {getValue()}
          </span>
        );
      }
    }
  ], []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ marginBottom: '20px', color: '#333' }}>
        AG Grid 风格的虚拟表格优化示例
      </h1>
      
      {/* 配置面板 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: '#f5f5f5', 
        borderRadius: '8px',
        display: 'flex',
        flexWrap: 'wrap',
        gap: '15px',
        alignItems: 'center'
      }}>
        <div>
          <label style={{ marginRight: '8px', fontWeight: 'bold' }}>数据量:</label>
          <select 
            value={dataSize} 
            onChange={(e) => setDataSize(Number(e.target.value))}
            style={{ padding: '4px 8px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            <option value={1000}>1,000 行</option>
            <option value={5000}>5,000 行</option>
            <option value={10000}>10,000 行</option>
            <option value={50000}>50,000 行</option>
            <option value={100000}>100,000 行</option>
          </select>
        </div>
        
        <div>
          <label style={{ marginRight: '8px', fontWeight: 'bold' }}>行高:</label>
          <input 
            type="number" 
            value={tableConfig.rowHeight}
            onChange={(e) => setTableConfig(prev => ({ ...prev, rowHeight: Number(e.target.value) }))}
            style={{ width: '60px', padding: '4px', borderRadius: '4px', border: '1px solid #ccc' }}
            min="30"
            max="100"
          />
        </div>
        
        <div>
          <label style={{ marginRight: '8px', fontWeight: 'bold' }}>缓冲区:</label>
          <input 
            type="number" 
            value={tableConfig.rowBuffer}
            onChange={(e) => setTableConfig(prev => ({ ...prev, rowBuffer: Number(e.target.value) }))}
            style={{ width: '60px', padding: '4px', borderRadius: '4px', border: '1px solid #ccc' }}
            min="5"
            max="50"
          />
        </div>
        
        <div>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input 
              type="checkbox" 
              checked={tableConfig.debounceVerticalScrollbar}
              onChange={(e) => setTableConfig(prev => ({ ...prev, debounceVerticalScrollbar: e.target.checked }))}
              style={{ marginRight: '5px' }}
            />
            防抖滚动
          </label>
        </div>
        
        <div>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input 
              type="checkbox" 
              checked={tableConfig.suppressAnimationFrame}
              onChange={(e) => setTableConfig(prev => ({ ...prev, suppressAnimationFrame: e.target.checked }))}
              style={{ marginRight: '5px' }}
            />
            禁用动画帧
          </label>
        </div>
        
        <div>
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
            <input 
              type="checkbox" 
              checked={tableConfig.suppressMaxRenderedRowRestriction}
              onChange={(e) => setTableConfig(prev => ({ ...prev, suppressMaxRenderedRowRestriction: e.target.checked }))}
              style={{ marginRight: '5px' }}
            />
            无渲染限制
          </label>
        </div>
      </div>
      
      {/* 性能提示 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '10px', 
        backgroundColor: '#e3f2fd', 
        borderRadius: '4px',
        fontSize: '14px',
        color: '#1976d2'
      }}>
        <strong>优化特性:</strong> 
        动画帧调度 • 智能缓冲区 • 预测性渲染 • 滚动方向优化 • 防白屏机制
      </div>
      
      {/* 虚拟表格 */}
      <AgGridLikeTable
        data={data}
        columns={columns}
        height={600}
        {...tableConfig}
        alwaysShowVerticalScroll={true}
      />
      
      {/* 统计信息 */}
      <div style={{ 
        marginTop: '15px', 
        padding: '10px', 
        backgroundColor: '#f9f9f9', 
        borderRadius: '4px',
        fontSize: '13px',
        color: '#666'
      }}>
        总数据量: {data.length.toLocaleString()} 行 | 
        行高: {tableConfig.rowHeight}px | 
        缓冲区: {tableConfig.rowBuffer} 行 | 
        预估总高度: {(data.length * tableConfig.rowHeight / 1000).toFixed(1)}k px
      </div>
    </div>
  );
}

export default VirtualTableExample;
